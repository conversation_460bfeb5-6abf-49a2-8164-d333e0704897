# Block Checklists

A feature for PublishPress Checklists Pro that allows you to create checklist requirements based on Gutenberg blocks in your content.

## Overview

Block Checklists automatically detects all registered WordPress blocks and allows you to configure minimum and maximum requirements for each block type on a per-post-type basis.

## Features

- **Automatic Block Detection**: Discovers all registered WordPress blocks including core blocks, theme blocks, and plugin blocks
- **Per Post Type Configuration**: Set different rules for posts, pages, and custom post types
- **Min/Max Requirements**: Configure minimum and maximum block counts with flexible rules
- **Real-time Validation**: Integrates with Gutenberg editor for live requirement checking
- **Visual Interface**: User-friendly admin interface with block icons and search/filter capabilities
- **Rule Types**: Support for display-only, warning, and blocking rules

## File Structure

```
src/block-checklists/
├── BlockChecklists.php              # Main class
├── lib/
│   ├── BlockRegistry.php            # Block discovery and management
│   └── Requirement/
│       └── Block_Counter.php        # Generic block requirement class
├── templates/
│   └── admin-page.php               # Admin interface template
├── assets/
│   ├── css/
│   │   └── admin-page.css           # Admin page styling
│   └── js/
│       ├── admin-page.js            # Admin page interactions
│       └── meta-box.js              # Gutenberg editor integration
└── README.md                        # This file
```

## How It Works

### 1. Block Discovery
The `BlockRegistry` class uses WordPress's `WP_Block_Type_Registry` to discover all registered blocks and extract their metadata including:
- Block name and title
- Category and namespace
- Icon and description
- Keywords for searching

### 2. Configuration Storage
Block requirements are stored in the WordPress options table using a flexible structure:
```php
[
    'core/paragraph' => [
        'post' => [
            'min' => 2,
            'max' => 10,
            'rule' => 'warning'
        ],
        'page' => [
            'min' => 1,
            'max' => 5,
            'rule' => 'block'
        ]
    ]
]
```

### 3. Requirement Processing
The `Block_Counter` class creates individual requirement instances for each configured block/post-type combination. Each instance:
- Counts blocks in post content using `parse_blocks()`
- Validates against min/max requirements
- Integrates with the existing checklist system

### 4. Real-time Integration
JavaScript integration provides real-time block counting in the Gutenberg editor:
- Monitors block changes using `wp.data.subscribe()`
- Updates requirement status dynamically
- Provides visual feedback to content creators

## Usage

### Admin Configuration
1. Navigate to **Checklists > Block Checklists** in the WordPress admin
2. Use search and filters to find specific blocks
3. Configure min/max requirements for each post type
4. Set rule type (disabled, display only, warning, or block publishing)
5. Save settings

### Content Creation
When editing posts/pages in Gutenberg:
1. Block requirements appear in the Checklists panel
2. Real-time counting shows current vs. required block counts
3. Publishing is blocked/warned based on configured rules

## Integration Points

### With Existing Checklist System
- Uses existing `Interface_required` interface
- Integrates with `publishpress_checklists_requirement_list` filter
- Follows established requirement naming conventions
- Supports all existing rule types and post type configurations

### With WordPress Core
- Leverages `WP_Block_Type_Registry` for block discovery
- Uses `parse_blocks()` for content analysis
- Integrates with Gutenberg editor via `wp.data` API
- Follows WordPress coding standards and security practices

## Technical Details

### Performance Considerations
- Block registry results are cached to avoid repeated API calls
- JavaScript updates are throttled to prevent excessive processing
- Only meaningful configurations are stored and processed

### Scalability
- Single generic requirement class handles all block types
- Dynamic configuration prevents file proliferation
- Efficient block counting with recursive parsing

### Security
- All inputs are sanitized and validated
- Capability checks ensure proper permissions
- Nonce verification for AJAX requests

## Extensibility

The system is designed to be extensible:
- New block types are automatically discovered
- Custom block metadata can be added via filters
- Additional validation rules can be implemented
- UI can be enhanced with additional filters and actions

## Dependencies

- WordPress 5.0+ (for Gutenberg support)
- PublishPress Checklists (free version)
- PHP 7.2.5+

## Browser Support

- Modern browsers with ES6 support
- Gutenberg editor compatibility
- Responsive design for mobile/tablet admin usage
