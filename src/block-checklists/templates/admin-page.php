<?php
/**
 * Admin page template for Block Checklists
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Variables available:
// $blocks - array of available blocks
// $postTypes - array of supported post types
// $settings - current block requirements settings
?>

<div class="wrap ppch-block-checklists">
    <h1><?php esc_html_e('Block Checklists', 'publishpress-checklists-pro'); ?></h1>

    <p class="description">
        <?php esc_html_e('Configure minimum and maximum requirements for Gutenberg blocks in your content. Set different rules for each post type.', 'publishpress-checklists-pro'); ?>
    </p>

    <form method="post" action="">
        <?php wp_nonce_field('ppch_block_checklists_save'); ?>

        <!-- Post Type Tabs -->
        <ul id="ppch-post-type-filter" class="nav-tab-wrapper">
            <?php foreach ($postTypes as $postType => $postTypeLabel) : ?>
                <li class="nav-tab post-type-<?php echo esc_attr($postType); ?>">
                    <a href="#<?php echo esc_attr($postType); ?>"><?php echo esc_html($postTypeLabel); ?></a>
                </li>
            <?php endforeach; ?>
        </ul>

        <div class="ppch-block-filters">
            <div class="ppch-filter-row">
                <input type="text"
                       id="ppch-search-blocks"
                       placeholder="<?php esc_attr_e('Search blocks...', 'publishpress-checklists-pro'); ?>"
                       class="ppch-search-input" />

                <select id="ppch-filter-category" class="ppch-filter-select">
                    <option value=""><?php esc_html_e('All Categories', 'publishpress-checklists-pro'); ?></option>
                    <?php
                    $categories = [];
                    foreach ($blocks as $block) {
                        $categories[$block['category']] = $block['category'];
                    }
                    ksort($categories);
                    foreach ($categories as $category) {
                        echo '<option value="' . esc_attr($category) . '">' . esc_html($category) . '</option>';
                    }
                    ?>
                </select>

                <select id="ppch-filter-namespace" class="ppch-filter-select">
                    <option value=""><?php esc_html_e('All Sources', 'publishpress-checklists-pro'); ?></option>
                    <option value="core"><?php esc_html_e('WordPress Core', 'publishpress-checklists-pro'); ?></option>
                    <option value="third-party"><?php esc_html_e('Third Party', 'publishpress-checklists-pro'); ?></option>
                </select>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="ppch-tabs-wrapper">
            <?php $i = 0; foreach ($postTypes as $postType => $postTypeLabel) : ?>
                <div id="ppch-tab-<?php echo esc_attr($postType); ?>" class="ppch-tab-content <?php echo $i === 0 ? 'active' : ''; ?>">

                    <?php if (empty($blocks)) : ?>
                        <div class="ppch-no-results">
                            <p><?php esc_html_e('No blocks found.', 'publishpress-checklists-pro'); ?></p>
                        </div>
                    <?php else : ?>
                        <div class="ppch-blocks-grid">
                            <?php foreach ($blocks as $blockName => $block) : ?>
                                <div class="ppch-block-card"
                                     data-block-name="<?php echo esc_attr($blockName); ?>"
                                     data-title="<?php echo esc_attr(strtolower($block['title'])); ?>"
                                     data-keywords="<?php echo esc_attr(strtolower(implode(' ', $block['keywords']))); ?>"
                                     data-category="<?php echo esc_attr($block['category']); ?>"
                                     data-namespace="<?php echo esc_attr($block['namespace']); ?>">

                                    <div class="ppch-block-icon">
                                        <?php echo $block['icon']['html']; ?>
                                    </div>

                                    <div class="ppch-block-title"><?php echo esc_html($block['title']); ?></div>

                                    <button type="button"
                                            class="ppch-settings-btn"
                                            data-block="<?php echo esc_attr($blockName); ?>"
                                            data-post-type="<?php echo esc_attr($postType); ?>"
                                            title="<?php esc_attr_e('Configure block settings', 'publishpress-checklists-pro'); ?>">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                    </button>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php $i++; endforeach; ?>
        </div>

        <div class="ppch-no-results" style="display: none;">
            <p><?php esc_html_e('No blocks found matching your search criteria.', 'publishpress-checklists-pro'); ?></p>
        </div>

        <!-- Settings Modal -->
        <div id="ppch-settings-modal" class="ppch-modal" style="display: none;">
            <div class="ppch-modal-content">
                <div class="ppch-modal-header">
                    <h3 id="ppch-modal-title"><?php esc_html_e('Block Settings', 'publishpress-checklists-pro'); ?></h3>
                    <button type="button" class="ppch-modal-close">&times;</button>
                </div>
                <div class="ppch-modal-body">
                    <div class="ppch-setting-row">
                        <label for="ppch-modal-rule"><?php esc_html_e('Rule:', 'publishpress-checklists-pro'); ?></label>
                        <select id="ppch-modal-rule" class="ppch-modal-select">
                            <option value="off"><?php esc_html_e('Disabled', 'publishpress-checklists-pro'); ?></option>
                            <option value="only_display"><?php esc_html_e('Recommended', 'publishpress-checklists-pro'); ?></option>
                            <option value="warning"><?php esc_html_e('Required', 'publishpress-checklists-pro'); ?></option>
                            <option value="block"><?php esc_html_e('Required (Block)', 'publishpress-checklists-pro'); ?></option>
                        </select>
                    </div>

                    <div id="ppch-modal-count-settings" class="ppch-count-settings" style="display: none;">
                        <div class="ppch-setting-row">
                            <label for="ppch-modal-min"><?php esc_html_e('Minimum:', 'publishpress-checklists-pro'); ?></label>
                            <input type="number" id="ppch-modal-min" min="0" class="ppch-number-input" placeholder="0" />
                        </div>

                        <div class="ppch-setting-row">
                            <label for="ppch-modal-max"><?php esc_html_e('Maximum:', 'publishpress-checklists-pro'); ?></label>
                            <input type="number" id="ppch-modal-max" min="0" class="ppch-number-input" placeholder="0" />
                        </div>
                    </div>
                </div>
                <div class="ppch-modal-footer">
                    <button type="button" class="button button-primary" id="ppch-modal-save"><?php esc_html_e('Save', 'publishpress-checklists-pro'); ?></button>
                    <button type="button" class="button" id="ppch-modal-cancel"><?php esc_html_e('Cancel', 'publishpress-checklists-pro'); ?></button>
                </div>
            </div>
        </div>

        <!-- Hidden form fields for settings -->
        <div id="ppch-hidden-fields" style="display: none;">
            <?php foreach ($blocks as $blockName => $block) : ?>
                <?php foreach ($postTypes as $postType => $postTypeLabel) : ?>
                    <?php
                    $currentMin = isset($settings[$blockName][$postType]['min']) ? $settings[$blockName][$postType]['min'] : '';
                    $currentMax = isset($settings[$blockName][$postType]['max']) ? $settings[$blockName][$postType]['max'] : '';
                    $currentRule = isset($settings[$blockName][$postType]['rule']) ? $settings[$blockName][$postType]['rule'] : 'off';
                    ?>
                    <input type="hidden"
                           name="block_requirements[<?php echo esc_attr($blockName); ?>][<?php echo esc_attr($postType); ?>][rule]"
                           value="<?php echo esc_attr($currentRule); ?>"
                           data-block="<?php echo esc_attr($blockName); ?>"
                           data-post-type="<?php echo esc_attr($postType); ?>"
                           data-field="rule" />
                    <input type="hidden"
                           name="block_requirements[<?php echo esc_attr($blockName); ?>][<?php echo esc_attr($postType); ?>][min]"
                           value="<?php echo esc_attr($currentMin); ?>"
                           data-block="<?php echo esc_attr($blockName); ?>"
                           data-post-type="<?php echo esc_attr($postType); ?>"
                           data-field="min" />
                    <input type="hidden"
                           name="block_requirements[<?php echo esc_attr($blockName); ?>][<?php echo esc_attr($postType); ?>][max]"
                           value="<?php echo esc_attr($currentMax); ?>"
                           data-block="<?php echo esc_attr($blockName); ?>"
                           data-post-type="<?php echo esc_attr($postType); ?>"
                           data-field="max" />
                <?php endforeach; ?>
            <?php endforeach; ?>
        </div>

        <?php submit_button(esc_html__('Save Block Requirements', 'publishpress-checklists-pro')); ?>
    </form>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    var currentBlock = '';
    var currentPostType = '';

    // Tab functionality
    $('#ppch-post-type-filter .nav-tab a').on('click', function(e) {
        e.preventDefault();
        var postType = $(this).attr('href').substring(1);

        // Update tab appearance
        $('#ppch-post-type-filter .nav-tab').removeClass('nav-tab-active');
        $(this).parent().addClass('nav-tab-active');

        // Show/hide tab content
        $('.ppch-tab-content').removeClass('active').hide();
        $('#ppch-tab-' + postType).addClass('active').show();

        // Re-apply filters
        filterBlocks();
    });

    // Initialize first tab as active
    $('#ppch-post-type-filter .nav-tab:first').addClass('nav-tab-active');
    $('.ppch-tab-content:first').show();

    // Settings button click
    $(document).on('click', '.ppch-settings-btn', function() {
        currentBlock = $(this).data('block');
        currentPostType = $(this).data('post-type');

        // Get current settings
        var ruleField = $('input[data-block="' + currentBlock + '"][data-post-type="' + currentPostType + '"][data-field="rule"]');
        var minField = $('input[data-block="' + currentBlock + '"][data-post-type="' + currentPostType + '"][data-field="min"]');
        var maxField = $('input[data-block="' + currentBlock + '"][data-post-type="' + currentPostType + '"][data-field="max"]');

        // Populate modal
        $('#ppch-modal-title').text(currentBlock + ' - ' + currentPostType);
        $('#ppch-modal-rule').val(ruleField.val());
        $('#ppch-modal-min').val(minField.val());
        $('#ppch-modal-max').val(maxField.val());

        // Show/hide count settings
        if (ruleField.val() === 'off') {
            $('#ppch-modal-count-settings').hide();
        } else {
            $('#ppch-modal-count-settings').show();
        }

        // Show modal
        $('#ppch-settings-modal').show();
    });

    // Modal rule change
    $('#ppch-modal-rule').on('change', function() {
        if ($(this).val() === 'off') {
            $('#ppch-modal-count-settings').hide();
        } else {
            $('#ppch-modal-count-settings').show();
        }
    });

    // Modal save
    $('#ppch-modal-save').on('click', function() {
        // Update hidden fields
        $('input[data-block="' + currentBlock + '"][data-post-type="' + currentPostType + '"][data-field="rule"]').val($('#ppch-modal-rule').val());
        $('input[data-block="' + currentBlock + '"][data-post-type="' + currentPostType + '"][data-field="min"]').val($('#ppch-modal-min').val());
        $('input[data-block="' + currentBlock + '"][data-post-type="' + currentPostType + '"][data-field="max"]').val($('#ppch-modal-max').val());

        // Close modal
        $('#ppch-settings-modal').hide();
    });

    // Modal cancel/close
    $('#ppch-modal-cancel, .ppch-modal-close').on('click', function() {
        $('#ppch-settings-modal').hide();
    });

    // Close modal on outside click
    $('#ppch-settings-modal').on('click', function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });

    // Search functionality
    $('#ppch-search-blocks').on('input', function() {
        filterBlocks();
    });

    // Category filter
    $('#ppch-filter-category').on('change', function() {
        filterBlocks();
    });

    // Namespace filter
    $('#ppch-filter-namespace').on('change', function() {
        filterBlocks();
    });

    function filterBlocks() {
        var searchTerm = $('#ppch-search-blocks').val().toLowerCase();
        var selectedCategory = $('#ppch-filter-category').val();
        var selectedNamespace = $('#ppch-filter-namespace').val();
        var visibleBlocks = 0;

        $('.ppch-block-card').each(function() {
            var $block = $(this);
            var blockTitle = $block.data('title');
            var blockName = $block.data('block-name').toLowerCase();
            var blockKeywords = $block.data('keywords');
            var blockCategory = $block.data('category');
            var blockNamespace = $block.data('namespace');

            var matchesSearch = !searchTerm ||
                               blockTitle.indexOf(searchTerm) !== -1 ||
                               blockName.indexOf(searchTerm) !== -1 ||
                               blockKeywords.indexOf(searchTerm) !== -1;

            var matchesCategory = !selectedCategory || blockCategory === selectedCategory;

            var matchesNamespace = !selectedNamespace ||
                                  (selectedNamespace === 'core' && blockNamespace === 'core') ||
                                  (selectedNamespace === 'third-party' && blockNamespace !== 'core');

            if (matchesSearch && matchesCategory && matchesNamespace) {
                $block.show();
                visibleBlocks++;
            } else {
                $block.hide();
            }
        });

        // Show/hide no results message
        if (visibleBlocks === 0) {
            $('.ppch-no-results').show();
        } else {
            $('.ppch-no-results').hide();
        }
    }
});
</script>
