<?php
/**
 * Admin page template for Block Checklists
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Variables available:
// $blocks - array of available blocks
// $postTypes - array of supported post types
// $settings - current block requirements settings
?>

<div class="wrap ppch-block-checklists">
    <h1><?php esc_html_e('Block Checklists', 'publishpress-checklists-pro'); ?></h1>
    
    <p class="description">
        <?php esc_html_e('Configure minimum and maximum requirements for Gutenberg blocks in your content. Set different rules for each post type.', 'publishpress-checklists-pro'); ?>
    </p>

    <form method="post" action="">
        <?php wp_nonce_field('ppch_block_checklists_save'); ?>
        
        <div class="ppch-block-filters">
            <div class="ppch-filter-group">
                <label for="ppch-search-blocks"><?php esc_html_e('Search Blocks:', 'publishpress-checklists-pro'); ?></label>
                <input type="text" id="ppch-search-blocks" placeholder="<?php esc_attr_e('Type to search blocks...', 'publishpress-checklists-pro'); ?>" />
            </div>
            
            <div class="ppch-filter-group">
                <label for="ppch-filter-category"><?php esc_html_e('Filter by Category:', 'publishpress-checklists-pro'); ?></label>
                <select id="ppch-filter-category">
                    <option value=""><?php esc_html_e('All Categories', 'publishpress-checklists-pro'); ?></option>
                    <?php
                    $categories = [];
                    foreach ($blocks as $block) {
                        $categories[$block['category']] = $block['category'];
                    }
                    ksort($categories);
                    foreach ($categories as $category) {
                        echo '<option value="' . esc_attr($category) . '">' . esc_html($category) . '</option>';
                    }
                    ?>
                </select>
            </div>
            
            <div class="ppch-filter-group">
                <label for="ppch-filter-namespace"><?php esc_html_e('Filter by Source:', 'publishpress-checklists-pro'); ?></label>
                <select id="ppch-filter-namespace">
                    <option value=""><?php esc_html_e('All Sources', 'publishpress-checklists-pro'); ?></option>
                    <option value="core"><?php esc_html_e('WordPress Core', 'publishpress-checklists-pro'); ?></option>
                    <option value="third-party"><?php esc_html_e('Third Party', 'publishpress-checklists-pro'); ?></option>
                </select>
            </div>
        </div>

        <div class="ppch-blocks-container">
            <?php
            $blocksByCategory = [];
            foreach ($blocks as $blockName => $block) {
                $blocksByCategory[$block['category']][$blockName] = $block;
            }
            ksort($blocksByCategory);

            foreach ($blocksByCategory as $category => $categoryBlocks) :
            ?>
                <div class="ppch-category-section" data-category="<?php echo esc_attr($category); ?>">
                    <h2 class="ppch-category-title"><?php echo esc_html($category); ?></h2>
                    
                    <div class="ppch-blocks-grid">
                        <?php foreach ($categoryBlocks as $blockName => $block) : ?>
                            <div class="ppch-block-item" 
                                 data-block-name="<?php echo esc_attr($blockName); ?>"
                                 data-category="<?php echo esc_attr($block['category']); ?>"
                                 data-namespace="<?php echo esc_attr($block['namespace']); ?>"
                                 data-title="<?php echo esc_attr(strtolower($block['title'])); ?>"
                                 data-keywords="<?php echo esc_attr(strtolower(implode(' ', $block['keywords']))); ?>">
                                
                                <div class="ppch-block-header">
                                    <div class="ppch-block-icon">
                                        <?php echo $block['icon']['html']; ?>
                                    </div>
                                    <div class="ppch-block-info">
                                        <h3 class="ppch-block-title"><?php echo esc_html($block['title']); ?></h3>
                                        <p class="ppch-block-name"><?php echo esc_html($blockName); ?></p>
                                        <?php if (!empty($block['description'])) : ?>
                                            <p class="ppch-block-description"><?php echo esc_html($block['description']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="ppch-block-settings">
                                    <?php foreach ($postTypes as $postType => $postTypeLabel) : ?>
                                        <div class="ppch-post-type-settings" data-post-type="<?php echo esc_attr($postType); ?>">
                                            <h4><?php echo esc_html($postTypeLabel); ?></h4>
                                            
                                            <?php
                                            $currentMin = isset($settings[$blockName][$postType]['min']) ? $settings[$blockName][$postType]['min'] : 0;
                                            $currentMax = isset($settings[$blockName][$postType]['max']) ? $settings[$blockName][$postType]['max'] : 0;
                                            $currentRule = isset($settings[$blockName][$postType]['rule']) ? $settings[$blockName][$postType]['rule'] : 'off';
                                            ?>
                                            
                                            <div class="ppch-setting-row">
                                                <label><?php esc_html_e('Rule:', 'publishpress-checklists-pro'); ?></label>
                                                <select name="block_requirements[<?php echo esc_attr($blockName); ?>][<?php echo esc_attr($postType); ?>][rule]" class="ppch-rule-select">
                                                    <option value="off" <?php selected($currentRule, 'off'); ?>><?php esc_html_e('Disabled', 'publishpress-checklists-pro'); ?></option>
                                                    <option value="only_display" <?php selected($currentRule, 'only_display'); ?>><?php esc_html_e('Display Only', 'publishpress-checklists-pro'); ?></option>
                                                    <option value="warning" <?php selected($currentRule, 'warning'); ?>><?php esc_html_e('Warning', 'publishpress-checklists-pro'); ?></option>
                                                    <option value="block" <?php selected($currentRule, 'block'); ?>><?php esc_html_e('Block Publishing', 'publishpress-checklists-pro'); ?></option>
                                                </select>
                                            </div>
                                            
                                            <div class="ppch-setting-row ppch-count-settings" <?php echo $currentRule === 'off' ? 'style="display:none;"' : ''; ?>>
                                                <label><?php esc_html_e('Min:', 'publishpress-checklists-pro'); ?></label>
                                                <input type="number" 
                                                       name="block_requirements[<?php echo esc_attr($blockName); ?>][<?php echo esc_attr($postType); ?>][min]" 
                                                       value="<?php echo esc_attr($currentMin); ?>" 
                                                       min="0" 
                                                       class="ppch-number-input" />
                                                
                                                <label><?php esc_html_e('Max:', 'publishpress-checklists-pro'); ?></label>
                                                <input type="number" 
                                                       name="block_requirements[<?php echo esc_attr($blockName); ?>][<?php echo esc_attr($postType); ?>][max]" 
                                                       value="<?php echo esc_attr($currentMax); ?>" 
                                                       min="0" 
                                                       class="ppch-number-input" />
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="ppch-no-results" style="display: none;">
            <p><?php esc_html_e('No blocks found matching your search criteria.', 'publishpress-checklists-pro'); ?></p>
        </div>

        <?php submit_button(esc_html__('Save Block Requirements', 'publishpress-checklists-pro')); ?>
    </form>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Show/hide count settings based on rule selection
    $('.ppch-rule-select').on('change', function() {
        var $this = $(this);
        var $countSettings = $this.closest('.ppch-post-type-settings').find('.ppch-count-settings');
        
        if ($this.val() === 'off') {
            $countSettings.hide();
        } else {
            $countSettings.show();
        }
    });

    // Search functionality
    $('#ppch-search-blocks').on('input', function() {
        filterBlocks();
    });

    // Category filter
    $('#ppch-filter-category').on('change', function() {
        filterBlocks();
    });

    // Namespace filter
    $('#ppch-filter-namespace').on('change', function() {
        filterBlocks();
    });

    function filterBlocks() {
        var searchTerm = $('#ppch-search-blocks').val().toLowerCase();
        var selectedCategory = $('#ppch-filter-category').val();
        var selectedNamespace = $('#ppch-filter-namespace').val();
        
        var visibleBlocks = 0;
        var visibleCategories = {};

        $('.ppch-block-item').each(function() {
            var $block = $(this);
            var blockTitle = $block.data('title');
            var blockName = $block.data('block-name').toLowerCase();
            var blockKeywords = $block.data('keywords');
            var blockCategory = $block.data('category');
            var blockNamespace = $block.data('namespace');
            
            var matchesSearch = !searchTerm || 
                               blockTitle.indexOf(searchTerm) !== -1 || 
                               blockName.indexOf(searchTerm) !== -1 || 
                               blockKeywords.indexOf(searchTerm) !== -1;
            
            var matchesCategory = !selectedCategory || blockCategory === selectedCategory;
            
            var matchesNamespace = !selectedNamespace || 
                                  (selectedNamespace === 'core' && blockNamespace === 'core') ||
                                  (selectedNamespace === 'third-party' && blockNamespace !== 'core');
            
            if (matchesSearch && matchesCategory && matchesNamespace) {
                $block.show();
                visibleBlocks++;
                visibleCategories[blockCategory] = true;
            } else {
                $block.hide();
            }
        });

        // Show/hide category sections
        $('.ppch-category-section').each(function() {
            var $section = $(this);
            var category = $section.data('category');
            
            if (visibleCategories[category]) {
                $section.show();
            } else {
                $section.hide();
            }
        });

        // Show/hide no results message
        if (visibleBlocks === 0) {
            $('.ppch-no-results').show();
        } else {
            $('.ppch-no-results').hide();
        }
    }
});
</script>
