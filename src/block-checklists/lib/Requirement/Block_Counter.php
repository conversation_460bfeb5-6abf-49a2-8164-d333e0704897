<?php
/**
 * Block Counter requirement for PublishPress Checklists Pro
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists\Requirement
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

namespace PublishPress\ChecklistsPro\BlockChecklists\Requirement;

if (!defined('ABSPATH')) {
    exit;
}

// Check if required classes exist before proceeding
if (!class_exists('PublishPress\Checklists\Core\Requirement\Base_counter') ||
    !interface_exists('PublishPress\Checklists\Core\Requirement\Interface_required')) {
    return;
}

use PublishPress\Checklists\Core\Requirement\Base_counter;
use PublishPress\Checklists\Core\Requirement\Interface_required;
use PublishPress\Checklists\Core\Plugin;

/**
 * Class Block_Counter
 *
 * Generic requirement class for counting blocks in Gutenberg editor
 */
class Block_Counter extends Base_counter implements Interface_required
{
    /**
     * The block name this requirement is for
     *
     * @var string
     */
    private $blockName;

    /**
     * The post type this requirement applies to
     *
     * @var string
     */
    private $postType;

    /**
     * The configuration for this requirement
     *
     * @var array
     */
    private $config;

    /**
     * The requirement name (sanitized block name)
     *
     * @var string
     */
    public $name;

    /**
     * The requirement group
     *
     * @var string
     */
    public $group = 'block-checklists';

    /**
     * The requirement type
     *
     * @var string
     */
    protected $type = 'block_counter';

    /**
     * Language strings
     *
     * @var array
     */
    public $lang = [];

    /**
     * Constructor
     *
     * @param string $blockName The block name (e.g., 'core/paragraph')
     * @param string $module The module name
     * @param string $postType The post type
     * @param array $config The configuration array
     */
    public function __construct($blockName, $module, $postType, $config)
    {
        $this->blockName = $blockName;
        $this->config = $config;
        $this->name = $this->sanitizeBlockName($blockName);

        // Set unit text for the counter
        $this->unitText = __('blocks', 'publishpress-checklists-pro');

        // Call parent constructor
        parent::__construct($module, $postType);
    }

    /**
     * Override to use our custom configuration instead of default options
     *
     * @param array $default_options
     * @return array
     */
    public function filter_default_options($default_options)
    {
        // Block checklists are managed separately, so we don't need to inject default options
        return $default_options;
    }

    /**
     * Override to use our custom configuration instead of settings validation
     *
     * @param array $new_options
     * @return array
     */
    public function filter_settings_validate($new_options)
    {
        // Block checklists are managed separately, so we don't need to validate here
        return $new_options;
    }

    /**
     * Initialize language strings
     */
    public function init_language()
    {
        $blockTitle = $this->getBlockTitle();

        $this->lang['label_min'] = sprintf(
            esc_html__('At least %d %s block(s)', 'publishpress-checklists-pro'),
            '%d',
            $blockTitle
        );

        $this->lang['label_max'] = sprintf(
            esc_html__('At most %d %s block(s)', 'publishpress-checklists-pro'),
            '%d',
            $blockTitle
        );

        $this->lang['label_exact'] = sprintf(
            esc_html__('Exactly %d %s block(s)', 'publishpress-checklists-pro'),
            '%d',
            $blockTitle
        );

        $this->lang['label_between'] = sprintf(
            esc_html__('Between %d and %d %s block(s)', 'publishpress-checklists-pro'),
            '%d',
            '%d',
            $blockTitle
        );
    }

    /**
     * Get the block title for display
     *
     * @return string
     */
    private function getBlockTitle()
    {
        // Try to get the block title from the registry
        if (class_exists('WP_Block_Type_Registry')) {
            $registry = \WP_Block_Type_Registry::get_instance();
            $blockType = $registry->get_registered($this->blockName);

            if ($blockType && isset($blockType->title)) {
                return $blockType->title;
            }
        }

        // Fallback: generate title from block name
        $parts = explode('/', $this->blockName);
        $blockSlug = end($parts);
        return ucwords(str_replace(['-', '_'], ' ', $blockSlug));
    }

    /**
     * Sanitize block name for use as requirement name
     *
     * @param string $blockName
     * @return string
     */
    private function sanitizeBlockName($blockName)
    {
        return 'block_' . str_replace(['/', '-'], '_', $blockName);
    }

    /**
     * Add the requirement to the list to be displayed in the meta box
     *
     * @param array $requirements
     * @param \stdClass $post
     * @return array
     */
    public function filter_requirements_list($requirements, $post)
    {
        // Check if it is a compatible post type
        if ($post->post_type !== $this->post_type) {
            return $requirements;
        }

        $min = isset($this->config['min']) ? (int)$this->config['min'] : 0;
        $max = isset($this->config['max']) ? (int)$this->config['max'] : 0;
        $rule = isset($this->config['rule']) ? $this->config['rule'] : 'off';

        // Skip if disabled
        if ($rule === 'off' || ($min === 0 && $max === 0)) {
            return $requirements;
        }

        $label = $this->generateLabel($min, $max);
        $status = $this->get_current_status($post, [$min, $max]);

        $requirements[$this->name] = [
            'status' => $status,
            'label' => $label,
            'value' => [$min, $max],
            'rule' => $rule,
            'type' => $this->type,
            'is_custom' => false,
            'block_name' => $this->blockName,
        ];

        return $requirements;
    }

    /**
     * Generate label based on min/max values
     *
     * @param int $min
     * @param int $max
     * @return string
     */
    private function generateLabel($min, $max)
    {
        if ($min === $max && $min > 0) {
            // Exact count
            return sprintf($this->lang['label_exact'], $min);
        } elseif ($min > 0 && $max > 0 && $max > $min) {
            // Range
            return sprintf($this->lang['label_between'], $min, $max);
        } elseif ($min > 0 && ($max === 0 || $max < $min)) {
            // Minimum only
            return sprintf($this->lang['label_min'], $min);
        } elseif ($max > 0 && $min === 0) {
            // Maximum only
            return sprintf($this->lang['label_max'], $max);
        }

        return sprintf($this->lang['label_min'], $min);
    }

    /**
     * Get current status of the requirement
     *
     * @param \stdClass $post
     * @param array $option_value [min, max]
     * @return bool
     */
    public function get_current_status($post, $option_value)
    {
        $count = $this->countBlocksInPost($post);
        $min = (int)$option_value[0];
        $max = (int)$option_value[1];

        // Both same value = exact
        if ($min === $max && $min > 0) {
            return $count === $min;
        }

        // Min not empty, max empty or < min = only min
        if ($min > 0 && ($max === 0 || $max < $min)) {
            return $count >= $min;
        }

        // Min not empty, max not empty and > min = both min and max
        if ($min > 0 && $max > 0 && $max > $min) {
            return $count >= $min && $count <= $max;
        }

        // Min empty, max not empty = only max
        if ($min === 0 && $max > 0) {
            return $count <= $max;
        }

        return false;
    }

    /**
     * Count blocks of the specified type in the post content
     *
     * @param \stdClass $post
     * @return int
     */
    private function countBlocksInPost($post)
    {
        if (empty($post->post_content)) {
            return 0;
        }

        // Parse blocks from post content
        if (function_exists('parse_blocks')) {
            $blocks = parse_blocks($post->post_content);
            return $this->countBlocksRecursive($blocks, $this->blockName);
        }

        return 0;
    }

    /**
     * Recursively count blocks of a specific type
     *
     * @param array $blocks
     * @param string $blockName
     * @return int
     */
    private function countBlocksRecursive($blocks, $blockName)
    {
        $count = 0;

        foreach ($blocks as $block) {
            if (isset($block['blockName']) && $block['blockName'] === $blockName) {
                $count++;
            }

            // Check inner blocks
            if (isset($block['innerBlocks']) && is_array($block['innerBlocks'])) {
                $count += $this->countBlocksRecursive($block['innerBlocks'], $blockName);
            }
        }

        return $count;
    }

    /**
     * Add the instance of the requirement class to the list
     *
     * @param array $requirements
     * @return array
     */
    public function filter_requirement_instances($requirements)
    {
        if (!empty($this->name)) {
            $requirements[$this->name] = $this;
        }

        return $requirements;
    }

    /**
     * Get the block name
     *
     * @return string
     */
    public function getBlockName()
    {
        return $this->blockName;
    }

    /**
     * Get the post type
     *
     * @return string
     */
    public function getPostType()
    {
        return $this->post_type;
    }

    /**
     * Get the configuration
     *
     * @return array
     */
    public function getConfig()
    {
        return $this->config;
    }
}
