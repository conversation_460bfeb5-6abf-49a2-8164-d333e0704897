<?php
/**
 * Block Registry for discovering and managing WordPress blocks
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

namespace PublishPress\ChecklistsPro\BlockChecklists;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class BlockRegistry
 *
 * Handles discovery and management of WordPress blocks
 */
class BlockRegistry
{
    /**
     * Cache for registered blocks
     *
     * @var array|null
     */
    private $cachedBlocks = null;

    /**
     * Get all registered WordPress blocks
     *
     * @return array Array of block information
     */
    public function getRegisteredBlocks()
    {
        if ($this->cachedBlocks !== null) {
            return $this->cachedBlocks;
        }

        $blocks = [];
        
        if (!class_exists('WP_Block_Type_Registry')) {
            return $blocks;
        }

        $registry = \WP_Block_Type_Registry::get_instance();
        $registeredBlocks = $registry->get_all_registered();

        foreach ($registeredBlocks as $blockName => $blockType) {
            $blocks[$blockName] = $this->formatBlockInfo($blockName, $blockType);
        }

        // Sort blocks by category and then by title
        uasort($blocks, function($a, $b) {
            if ($a['category'] === $b['category']) {
                return strcmp($a['title'], $b['title']);
            }
            return strcmp($a['category'], $b['category']);
        });

        $this->cachedBlocks = $blocks;
        return $blocks;
    }

    /**
     * Get information for a specific block
     *
     * @param string $blockName The block name
     * @return array|null Block information or null if not found
     */
    public function getBlockInfo($blockName)
    {
        $blocks = $this->getRegisteredBlocks();
        return isset($blocks[$blockName]) ? $blocks[$blockName] : null;
    }

    /**
     * Format block information for consistent structure
     *
     * @param string $blockName The block name
     * @param \WP_Block_Type $blockType The block type object
     * @return array Formatted block information
     */
    private function formatBlockInfo($blockName, $blockType)
    {
        return [
            'name' => $blockName,
            'title' => $this->getBlockTitle($blockType),
            'description' => $this->getBlockDescription($blockType),
            'category' => $this->getBlockCategory($blockType),
            'icon' => $this->getBlockIcon($blockType),
            'keywords' => $this->getBlockKeywords($blockType),
            'supports' => $this->getBlockSupports($blockType),
            'namespace' => $this->getBlockNamespace($blockName),
        ];
    }

    /**
     * Get block title
     *
     * @param \WP_Block_Type $blockType
     * @return string
     */
    private function getBlockTitle($blockType)
    {
        if (isset($blockType->title)) {
            return $blockType->title;
        }
        
        // Fallback: generate title from block name
        $parts = explode('/', $blockType->name);
        $blockSlug = end($parts);
        return ucwords(str_replace(['-', '_'], ' ', $blockSlug));
    }

    /**
     * Get block description
     *
     * @param \WP_Block_Type $blockType
     * @return string
     */
    private function getBlockDescription($blockType)
    {
        return isset($blockType->description) ? $blockType->description : '';
    }

    /**
     * Get block category
     *
     * @param \WP_Block_Type $blockType
     * @return string
     */
    private function getBlockCategory($blockType)
    {
        if (isset($blockType->category)) {
            return $this->formatCategoryName($blockType->category);
        }
        
        // Determine category from namespace
        $namespace = $this->getBlockNamespace($blockType->name);
        
        switch ($namespace) {
            case 'core':
                return 'Core';
            case 'woocommerce':
                return 'WooCommerce';
            default:
                return 'Third Party';
        }
    }

    /**
     * Get block icon information
     *
     * @param \WP_Block_Type $blockType
     * @return array
     */
    private function getBlockIcon($blockType)
    {
        $icon = [
            'type' => 'dashicon',
            'value' => 'block-default',
            'html' => '<span class="dashicons dashicons-block-default"></span>'
        ];

        if (isset($blockType->icon)) {
            if (is_string($blockType->icon)) {
                // Simple dashicon name
                $icon['value'] = $blockType->icon;
                $icon['html'] = '<span class="dashicons dashicons-' . esc_attr($blockType->icon) . '"></span>';
            } elseif (is_array($blockType->icon)) {
                // Complex icon definition
                if (isset($blockType->icon['src'])) {
                    if (strpos($blockType->icon['src'], '<svg') === 0) {
                        $icon['type'] = 'svg';
                        $icon['value'] = $blockType->icon['src'];
                        $icon['html'] = $blockType->icon['src'];
                    } else {
                        $icon['value'] = $blockType->icon['src'];
                        $icon['html'] = '<span class="dashicons dashicons-' . esc_attr($blockType->icon['src']) . '"></span>';
                    }
                }
            }
        }

        return $icon;
    }

    /**
     * Get block keywords
     *
     * @param \WP_Block_Type $blockType
     * @return array
     */
    private function getBlockKeywords($blockType)
    {
        return isset($blockType->keywords) && is_array($blockType->keywords) ? $blockType->keywords : [];
    }

    /**
     * Get block supports
     *
     * @param \WP_Block_Type $blockType
     * @return array
     */
    private function getBlockSupports($blockType)
    {
        return isset($blockType->supports) && is_array($blockType->supports) ? $blockType->supports : [];
    }

    /**
     * Get block namespace from block name
     *
     * @param string $blockName
     * @return string
     */
    private function getBlockNamespace($blockName)
    {
        $parts = explode('/', $blockName);
        return isset($parts[0]) ? $parts[0] : 'unknown';
    }

    /**
     * Format category name for display
     *
     * @param string $category
     * @return string
     */
    private function formatCategoryName($category)
    {
        $categoryNames = [
            'text' => 'Text',
            'media' => 'Media',
            'design' => 'Design',
            'widgets' => 'Widgets',
            'theme' => 'Theme',
            'embed' => 'Embeds',
            'reusable' => 'Reusable',
            'common' => 'Common',
            'formatting' => 'Formatting',
            'layout' => 'Layout',
        ];

        return isset($categoryNames[$category]) ? $categoryNames[$category] : ucfirst($category);
    }

    /**
     * Get blocks grouped by category
     *
     * @return array
     */
    public function getBlocksByCategory()
    {
        $blocks = $this->getRegisteredBlocks();
        $grouped = [];

        foreach ($blocks as $blockName => $blockInfo) {
            $category = $blockInfo['category'];
            if (!isset($grouped[$category])) {
                $grouped[$category] = [];
            }
            $grouped[$category][$blockName] = $blockInfo;
        }

        // Sort categories
        ksort($grouped);

        return $grouped;
    }

    /**
     * Search blocks by name or title
     *
     * @param string $search Search term
     * @return array
     */
    public function searchBlocks($search)
    {
        $blocks = $this->getRegisteredBlocks();
        $results = [];

        $search = strtolower(trim($search));
        if (empty($search)) {
            return $blocks;
        }

        foreach ($blocks as $blockName => $blockInfo) {
            $searchableText = strtolower($blockInfo['title'] . ' ' . $blockInfo['name'] . ' ' . implode(' ', $blockInfo['keywords']));
            
            if (strpos($searchableText, $search) !== false) {
                $results[$blockName] = $blockInfo;
            }
        }

        return $results;
    }

    /**
     * Get core WordPress blocks only
     *
     * @return array
     */
    public function getCoreBlocks()
    {
        $blocks = $this->getRegisteredBlocks();
        $coreBlocks = [];

        foreach ($blocks as $blockName => $blockInfo) {
            if ($blockInfo['namespace'] === 'core') {
                $coreBlocks[$blockName] = $blockInfo;
            }
        }

        return $coreBlocks;
    }

    /**
     * Get third-party blocks only
     *
     * @return array
     */
    public function getThirdPartyBlocks()
    {
        $blocks = $this->getRegisteredBlocks();
        $thirdPartyBlocks = [];

        foreach ($blocks as $blockName => $blockInfo) {
            if ($blockInfo['namespace'] !== 'core') {
                $thirdPartyBlocks[$blockName] = $blockInfo;
            }
        }

        return $thirdPartyBlocks;
    }

    /**
     * Clear the blocks cache
     */
    public function clearCache()
    {
        $this->cachedBlocks = null;
    }
}
