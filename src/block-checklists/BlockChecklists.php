<?php
/**
 * Block Checklists module for PublishPress Checklists Pro
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

namespace PublishPress\ChecklistsPro\BlockChecklists;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class BlockChecklists
 *
 * Adds the ability to create checklist requirements for Gutenberg blocks
 */
class BlockChecklists
{
    /**
     * Option name for storing block requirements
     *
     * @var string
     */
    const OPTION_NAME = 'publishpress_checklists_block_checklists_options';

    /**
     * Menu slug for the admin page
     *
     * @var string
     */
    const MENU_SLUG = 'ppch-block-checklists';

    /**
     * Block Registry instance
     *
     * @var BlockRegistry
     */
    private $blockRegistry;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Load dependencies
        $this->loadDependencies();

        // Initialize block registry
        $this->blockRegistry = new BlockRegistry();

        // Set up hooks
        $this->setHooks();
    }

    /**
     * Load required dependencies
     */
    private function loadDependencies()
    {
        require_once __DIR__ . '/lib/BlockRegistry.php';

        // Only load Block_Counter if the interface is available
        if (interface_exists('PublishPress\Checklists\Core\Requirement\Interface_required')) {
            require_once __DIR__ . '/lib/Requirement/Block_Counter.php';
        }
    }

    /**
     * Set up WordPress hooks
     */
    private function setHooks()
    {
        // Admin menu
        add_action('publishpress_checklists_admin_submenu', [$this, 'addAdminSubmenu']);

        // Admin scripts and styles
        add_action('admin_enqueue_scripts', [$this, 'enqueueAdminAssets']);

        // Block editor assets
        add_action('enqueue_block_editor_assets', [$this, 'enqueueBlockEditorAssets']);

        // Load requirements
        add_action('publishpress_checklists_load_requirements', [$this, 'loadBlockRequirements']);

        // AJAX handlers
        add_action('wp_ajax_ppch_save_block_requirements', [$this, 'ajaxSaveBlockRequirements']);
        add_action('wp_ajax_ppch_get_block_info', [$this, 'ajaxGetBlockInfo']);
    }

    /**
     * Add submenu page under Checklists
     */
    public function addAdminSubmenu()
    {
        if (!class_exists('\PublishPress\Checklists\Core\Factory')) {
            return;
        }

        $legacyPlugin = \PublishPress\Checklists\Core\Factory::getLegacyPlugin();

        add_submenu_page(
            $legacyPlugin->getMenuSlug(),
            esc_html__('Block Checklists', 'publishpress-checklists-pro'),
            esc_html__('Block Checklists', 'publishpress-checklists-pro'),
            apply_filters('publishpress_checklists_manage_checklist_cap', 'manage_checklists'),
            self::MENU_SLUG,
            [$this, 'renderAdminPage']
        );
    }

    /**
     * Render the admin page
     */
    public function renderAdminPage()
    {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['_wpnonce'], 'ppch_block_checklists_save')) {
            $this->saveBlockRequirements();
        }

        // Get available blocks
        $blocks = $this->blockRegistry->getRegisteredBlocks();

        // Get supported post types
        $postTypes = $this->getSupportedPostTypes();

        // Get current settings
        $settings = $this->getBlockRequirements();

        // Include template
        include __DIR__ . '/templates/admin-page.php';
    }

    /**
     * Get supported post types
     *
     * @return array
     */
    private function getSupportedPostTypes()
    {
        $postTypes = [];

        if (class_exists('\PublishPress\Checklists\Core\Factory')) {
            $legacyPlugin = \PublishPress\Checklists\Core\Factory::getLegacyPlugin();
            if (isset($legacyPlugin->settings)) {
                $supportedTypes = $legacyPlugin->settings->getSupportedPostTypesForModule();
                foreach ($supportedTypes as $postType => $args) {
                    $postTypes[$postType] = $args->label;
                }
            }
        }

        // Fallback to basic post types if the above doesn't work
        if (empty($postTypes)) {
            $postTypes = [
                'post' => 'Posts',
                'page' => 'Pages'
            ];
        }

        return $postTypes;
    }

    /**
     * Get current block requirements
     *
     * @return array
     */
    private function getBlockRequirements()
    {
        return get_option(self::OPTION_NAME, []);
    }

    /**
     * Save block requirements from form submission
     */
    private function saveBlockRequirements()
    {
        if (!current_user_can('manage_checklists')) {
            wp_die(esc_html__('You do not have sufficient permissions to access this page.', 'publishpress-checklists-pro'));
        }

        $settings = [];

        if (isset($_POST['block_requirements']) && is_array($_POST['block_requirements'])) {
            foreach ($_POST['block_requirements'] as $blockName => $postTypes) {
                if (!is_array($postTypes)) {
                    continue;
                }

                foreach ($postTypes as $postType => $config) {
                    if (!is_array($config)) {
                        continue;
                    }

                    $min = isset($config['min']) ? absint($config['min']) : 0;
                    $max = isset($config['max']) ? absint($config['max']) : 0;
                    $rule = isset($config['rule']) ? sanitize_text_field($config['rule']) : 'off';

                    // Only save if there's a meaningful configuration
                    if ($min > 0 || $max > 0 || $rule !== 'off') {
                        $settings[$blockName][$postType] = [
                            'min' => $min,
                            'max' => $max,
                            'rule' => $rule
                        ];
                    }
                }
            }
        }

        update_option(self::OPTION_NAME, $settings);

        // Show success message
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible"><p>' .
                 esc_html__('Block checklist settings saved successfully.', 'publishpress-checklists-pro') .
                 '</p></div>';
        });
    }

    /**
     * Load block requirements into the checklist system
     */
    public function loadBlockRequirements()
    {
        // Check if the interface is available
        if (!interface_exists('PublishPress\Checklists\Core\Requirement\Interface_required')) {
            return;
        }

        // Check if the Block_Counter class is available
        if (!class_exists('PublishPress\ChecklistsPro\BlockChecklists\Requirement\Block_Counter')) {
            return;
        }

        $settings = $this->getBlockRequirements();
        $postTypes = $this->getSupportedPostTypes();

        foreach ($settings as $blockName => $postTypeSettings) {
            foreach ($postTypeSettings as $postType => $config) {
                if (!isset($postTypes[$postType])) {
                    continue;
                }

                // Only create requirement if it has meaningful configuration
                $min = isset($config['min']) ? (int)$config['min'] : 0;
                $max = isset($config['max']) ? (int)$config['max'] : 0;
                $rule = isset($config['rule']) ? $config['rule'] : 'off';

                if ($rule !== 'off' && ($min > 0 || $max > 0)) {
                    // Create requirement instance
                    new Requirement\Block_Counter($blockName, $postType, $config);
                }
            }
        }
    }

    /**
     * Enqueue admin assets
     */
    public function enqueueAdminAssets($hook)
    {
        if (strpos($hook, self::MENU_SLUG) === false) {
            return;
        }

        $baseUrl = plugin_dir_url(__FILE__);
        $version = PPCHPRO_VERSION;

        wp_enqueue_style(
            'ppch-block-checklists-admin',
            $baseUrl . 'assets/css/admin-page.css',
            [],
            $version
        );

        wp_enqueue_script(
            'ppch-block-checklists-admin',
            $baseUrl . 'assets/js/admin-page.js',
            ['jquery'],
            $version,
            true
        );

        wp_localize_script('ppch-block-checklists-admin', 'ppchBlockChecklists', [
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ppch_block_checklists_ajax'),
            'strings' => [
                'loading' => esc_html__('Loading...', 'publishpress-checklists-pro'),
                'error' => esc_html__('An error occurred.', 'publishpress-checklists-pro'),
            ]
        ]);
    }

    /**
     * Enqueue block editor assets
     */
    public function enqueueBlockEditorAssets()
    {
        $baseUrl = plugin_dir_url(__FILE__);
        $version = PPCHPRO_VERSION;

        wp_enqueue_script(
            'ppch-block-checklists-editor',
            $baseUrl . 'assets/js/meta-box.js',
            ['wp-data', 'wp-blocks', 'jquery', 'pp-checklists-requirements'],
            $version,
            true
        );

        // Pass block requirements to JavaScript
        $settings = $this->getBlockRequirements();
        $currentPostType = get_post_type();

        $blockRequirements = [];
        foreach ($settings as $blockName => $postTypeSettings) {
            if (isset($postTypeSettings[$currentPostType])) {
                $blockRequirements[$blockName] = $postTypeSettings[$currentPostType];
            }
        }

        wp_localize_script('ppch-block-checklists-editor', 'ppchBlockRequirements', $blockRequirements);
    }

    /**
     * AJAX handler to save block requirements
     */
    public function ajaxSaveBlockRequirements()
    {
        if (!wp_verify_nonce($_POST['nonce'], 'ppch_block_checklists_ajax')) {
            wp_send_json_error('Invalid nonce');
        }

        if (!current_user_can('manage_checklists')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Handle AJAX save logic here if needed
        wp_send_json_success();
    }

    /**
     * AJAX handler to get block information
     */
    public function ajaxGetBlockInfo()
    {
        if (!wp_verify_nonce($_POST['nonce'], 'ppch_block_checklists_ajax')) {
            wp_send_json_error('Invalid nonce');
        }

        $blockName = sanitize_text_field($_POST['block_name'] ?? '');
        $blockInfo = $this->blockRegistry->getBlockInfo($blockName);

        wp_send_json_success($blockInfo);
    }
}
