/**
 * Admin page styles for Block Checklists
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

.ppch-block-checklists {
    max-width: 1200px;
}

.ppch-block-checklists h1 {
    margin-bottom: 10px;
}

.ppch-block-checklists .description {
    margin-bottom: 30px;
    font-size: 14px;
    color: #666;
}

/* Filters */
.ppch-block-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    flex-wrap: wrap;
}

.ppch-filter-group {
    display: flex;
    flex-direction: column;
    min-width: 200px;
}

.ppch-filter-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.ppch-filter-group input,
.ppch-filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.ppch-filter-group input:focus,
.ppch-filter-group select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

/* Category sections */
.ppch-category-section {
    margin-bottom: 40px;
}

.ppch-category-title {
    font-size: 20px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #0073aa;
    color: #333;
}

/* Blocks grid */
.ppch-blocks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

/* Block item */
.ppch-block-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
}

.ppch-block-item:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

/* Block header */
.ppch-block-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    gap: 15px;
}

.ppch-block-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    border-radius: 6px;
}

.ppch-block-icon .dashicons {
    font-size: 24px;
    color: #666;
}

.ppch-block-icon svg {
    width: 24px;
    height: 24px;
    fill: #666;
}

.ppch-block-info {
    flex: 1;
    min-width: 0;
}

.ppch-block-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #333;
}

.ppch-block-name {
    font-size: 12px;
    color: #666;
    margin: 0 0 8px 0;
    font-family: monospace;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}

.ppch-block-description {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* Block settings */
.ppch-block-settings {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.ppch-post-type-settings {
    margin-bottom: 15px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.ppch-post-type-settings:last-child {
    margin-bottom: 0;
}

.ppch-post-type-settings h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.ppch-setting-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.ppch-setting-row:last-child {
    margin-bottom: 0;
}

.ppch-setting-row label {
    font-size: 13px;
    font-weight: 500;
    color: #555;
    min-width: 40px;
}

.ppch-rule-select {
    min-width: 120px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.ppch-number-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
    text-align: center;
}

.ppch-count-settings {
    padding-left: 10px;
    border-left: 3px solid #0073aa;
    background: #fff;
    margin-top: 10px;
    padding-top: 10px;
    padding-bottom: 10px;
    border-radius: 0 4px 4px 0;
}

/* No results */
.ppch-no-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

/* Submit button */
.ppch-block-checklists .submit {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

/* Responsive design */
@media (max-width: 768px) {
    .ppch-blocks-grid {
        grid-template-columns: 1fr;
    }
    
    .ppch-block-filters {
        flex-direction: column;
        gap: 15px;
    }
    
    .ppch-filter-group {
        min-width: auto;
    }
    
    .ppch-block-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .ppch-setting-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .ppch-setting-row label {
        min-width: auto;
    }
}

/* Loading states */
.ppch-block-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.ppch-block-item.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/error states */
.ppch-block-item.success {
    border-color: #46b450;
    background-color: #f7fcf0;
}

.ppch-block-item.error {
    border-color: #dc3232;
    background-color: #fef7f1;
}

/* Accessibility improvements */
.ppch-block-item:focus-within {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.ppch-rule-select:focus,
.ppch-number-input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .ppch-block-item {
        border-width: 2px;
    }
    
    .ppch-block-icon {
        background: #000;
    }
    
    .ppch-block-icon .dashicons,
    .ppch-block-icon svg {
        color: #fff;
        fill: #fff;
    }
}
