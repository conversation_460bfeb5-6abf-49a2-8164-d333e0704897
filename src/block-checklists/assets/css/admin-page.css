/**
 * Admin page styles for Block Checklists
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

.ppch-block-checklists {
    max-width: 1200px;
}

.ppch-block-checklists h1 {
    margin-bottom: 10px;
}

.ppch-block-checklists .description {
    margin-bottom: 30px;
    font-size: 14px;
    color: #666;
}

/* Post Type Tabs */
#ppch-post-type-filter {
    margin-bottom: 20px;
}

#ppch-post-type-filter .nav-tab {
    margin-right: 5px;
}

/* Tab Content */
.ppch-tabs-wrapper {
    margin-bottom: 30px;
}

.ppch-tab-content {
    display: none;
}

.ppch-tab-content.active {
    display: block;
}

/* Filters */
.ppch-block-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    align-items: center;
    flex-wrap: wrap;
}

.ppch-filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
}

.ppch-search-input,
.ppch-filter-select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.ppch-search-input {
    min-width: 250px;
}

.ppch-filter-select {
    min-width: 150px;
}

.ppch-search-input:focus,
.ppch-filter-select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

/* Blocks grid - compact layout */
.ppch-blocks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    padding: 20px 0;
}

/* Block card - compact design */
.ppch-block-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}

.ppch-block-card:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
    transform: translateY(-2px);
}

/* Block icon in compact layout */
.ppch-block-card .ppch-block-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    border-radius: 6px;
    margin-bottom: 8px;
    flex-shrink: 0;
}

.ppch-block-card .ppch-block-icon .dashicons {
    font-size: 20px;
    color: #666;
}

.ppch-block-card .ppch-block-icon svg {
    width: 20px;
    height: 20px;
    fill: #666;
}

/* Block title in compact layout */
.ppch-block-card .ppch-block-title {
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
    line-height: 1.3;
    text-align: center;
    flex: 1;
}

/* Settings button */
.ppch-settings-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ppch-settings-btn:hover {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.ppch-settings-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.ppch-settings-btn:hover .dashicons {
    color: #fff;
}

/* Modal styles */
.ppch-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ppch-modal-content {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
}

.ppch-modal-header {
    padding: 20px 20px 15px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ppch-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.ppch-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.ppch-modal-close:hover {
    background: #f0f0f0;
    color: #333;
}

.ppch-modal-body {
    padding: 20px;
}

.ppch-setting-row {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.ppch-setting-row label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.ppch-modal-select,
.ppch-number-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.ppch-modal-select:focus,
.ppch-number-input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.ppch-number-input {
    width: 100px;
}

.ppch-count-settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.ppch-modal-footer {
    padding: 15px 20px 20px;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* No results */
.ppch-no-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

/* Submit button */
.ppch-block-checklists .submit {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

/* Responsive design */
@media (max-width: 768px) {
    .ppch-blocks-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 10px;
    }

    .ppch-filter-row {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .ppch-search-input,
    .ppch-filter-select {
        min-width: auto;
        width: 100%;
    }

    .ppch-block-card {
        min-height: 100px;
        padding: 12px;
    }

    .ppch-block-card .ppch-block-icon {
        width: 28px;
        height: 28px;
    }

    .ppch-block-card .ppch-block-icon .dashicons {
        font-size: 18px;
    }

    .ppch-block-card .ppch-block-icon svg {
        width: 18px;
        height: 18px;
    }

    .ppch-block-card .ppch-block-title {
        font-size: 12px;
    }

    .ppch-modal-content {
        width: 95%;
        margin: 20px;
    }

    .ppch-modal-header,
    .ppch-modal-body,
    .ppch-modal-footer {
        padding: 15px;
    }

    .ppch-modal-footer {
        flex-direction: column;
    }

    .ppch-modal-footer .button {
        width: 100%;
        margin: 0;
    }
}

/* Loading states */
.ppch-block-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.ppch-block-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/error states */
.ppch-block-card.success {
    border-color: #46b450;
    background-color: #f7fcf0;
}

.ppch-block-card.error {
    border-color: #dc3232;
    background-color: #fef7f1;
}

/* Accessibility improvements */
.ppch-block-card:focus-within {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.ppch-settings-btn:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .ppch-block-card {
        border-width: 2px;
    }

    .ppch-block-card .ppch-block-icon {
        background: #000;
    }

    .ppch-block-card .ppch-block-icon .dashicons,
    .ppch-block-card .ppch-block-icon svg {
        color: #fff;
        fill: #fff;
    }

    .ppch-settings-btn {
        border-width: 2px;
    }
}
