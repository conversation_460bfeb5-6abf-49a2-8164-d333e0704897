/**
 * Block Checklists integration for Gutenberg editor
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

(function($, window, document, PP_Checklists) {
    'use strict';

    // Ensure PP_Checklists is available
    if (typeof PP_Checklists === 'undefined') {
        console.warn('PP_Checklists not found. Block checklists integration disabled.');
        return;
    }

    /**
     * Block Checklists integration object
     */
    var PPCHBlockChecklists = {
        
        /**
         * Block requirements configuration
         */
        requirements: window.ppchBlockRequirements || {},
        
        /**
         * Cache for block counts
         */
        blockCounts: {},
        
        /**
         * Last update timestamp to prevent excessive updates
         */
        lastUpdate: 0,
        
        /**
         * Update throttle delay in milliseconds
         */
        updateDelay: 500,

        /**
         * Initialize the block checklists integration
         */
        init: function() {
            if (!PP_Checklists.is_gutenberg_active()) {
                return;
            }

            this.bindEvents();
            this.startBlockMonitoring();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Listen to the main checklist tick event
            $(document).on(PP_Checklists.EVENT_TIC, this.handleTick.bind(this));
            
            // Listen to block editor changes
            if (typeof wp !== 'undefined' && wp.data) {
                wp.data.subscribe(this.handleEditorChange.bind(this));
            }
        },

        /**
         * Start monitoring blocks in the editor
         */
        startBlockMonitoring: function() {
            // Initial count
            this.updateBlockCounts();
            
            // Set up periodic monitoring as fallback
            setInterval(this.updateBlockCounts.bind(this), 2000);
        },

        /**
         * Handle the main checklist tick event
         */
        handleTick: function() {
            this.updateBlockCounts();
        },

        /**
         * Handle editor changes
         */
        handleEditorChange: function() {
            var now = Date.now();
            
            // Throttle updates to prevent excessive processing
            if (now - this.lastUpdate < this.updateDelay) {
                return;
            }
            
            this.lastUpdate = now;
            this.updateBlockCounts();
        },

        /**
         * Update block counts and requirement statuses
         */
        updateBlockCounts: function() {
            if (!this.isEditorReady()) {
                return;
            }

            try {
                var blocks = this.getAllBlocks();
                var newCounts = this.countBlocksByType(blocks);
                
                // Check if counts have changed
                if (this.hasCountsChanged(newCounts)) {
                    this.blockCounts = newCounts;
                    this.updateRequirementStatuses();
                }
            } catch (error) {
                console.warn('Error updating block counts:', error);
            }
        },

        /**
         * Check if the editor is ready
         */
        isEditorReady: function() {
            return typeof wp !== 'undefined' && 
                   wp.data && 
                   wp.data.select('core/block-editor') &&
                   typeof wp.data.select('core/block-editor').getBlocks === 'function';
        },

        /**
         * Get all blocks from the editor
         */
        getAllBlocks: function() {
            if (!this.isEditorReady()) {
                return [];
            }

            return wp.data.select('core/block-editor').getBlocks();
        },

        /**
         * Count blocks by type recursively
         */
        countBlocksByType: function(blocks) {
            var counts = {};
            
            this.countBlocksRecursive(blocks, counts);
            
            return counts;
        },

        /**
         * Recursively count blocks
         */
        countBlocksRecursive: function(blocks, counts) {
            if (!Array.isArray(blocks)) {
                return;
            }

            blocks.forEach(function(block) {
                if (block && block.name) {
                    counts[block.name] = (counts[block.name] || 0) + 1;
                    
                    // Count inner blocks
                    if (block.innerBlocks && Array.isArray(block.innerBlocks)) {
                        this.countBlocksRecursive(block.innerBlocks, counts);
                    }
                }
            }.bind(this));
        },

        /**
         * Check if block counts have changed
         */
        hasCountsChanged: function(newCounts) {
            var oldKeys = Object.keys(this.blockCounts);
            var newKeys = Object.keys(newCounts);
            
            // Check if number of block types changed
            if (oldKeys.length !== newKeys.length) {
                return true;
            }
            
            // Check if any counts changed
            for (var blockName in newCounts) {
                if (this.blockCounts[blockName] !== newCounts[blockName]) {
                    return true;
                }
            }
            
            return false;
        },

        /**
         * Update requirement statuses based on current block counts
         */
        updateRequirementStatuses: function() {
            for (var blockName in this.requirements) {
                if (this.requirements.hasOwnProperty(blockName)) {
                    this.updateBlockRequirement(blockName);
                }
            }
        },

        /**
         * Update a specific block requirement
         */
        updateBlockRequirement: function(blockName) {
            var config = this.requirements[blockName];
            if (!config) {
                return;
            }

            var currentCount = this.blockCounts[blockName] || 0;
            var min = parseInt(config.min) || 0;
            var max = parseInt(config.max) || 0;
            
            var status = this.checkBlockRequirement(currentCount, min, max);
            var requirementName = this.getRequirementName(blockName);
            
            // Find and update the requirement element
            var $requirement = $('#pp-checklists-req-' + requirementName);
            if ($requirement.length) {
                $requirement.trigger(PP_Checklists.EVENT_UPDATE_REQUIREMENT_STATE, status);
                
                // Update count display if element exists
                this.updateCountDisplay($requirement, currentCount, min, max);
            }
        },

        /**
         * Check if block requirement is satisfied
         */
        checkBlockRequirement: function(count, min, max) {
            // Both same value = exact
            if (min === max && min > 0) {
                return count === min;
            }

            // Min not empty, max empty or < min = only min
            if (min > 0 && (max === 0 || max < min)) {
                return count >= min;
            }

            // Min not empty, max not empty and > min = both min and max
            if (min > 0 && max > 0 && max > min) {
                return count >= min && count <= max;
            }

            // Min empty, max not empty = only max
            if (min === 0 && max > 0) {
                return count <= max;
            }

            return false;
        },

        /**
         * Get requirement name from block name
         */
        getRequirementName: function(blockName) {
            return 'block_' + blockName.replace(/[\/\-]/g, '_');
        },

        /**
         * Update count display in requirement element
         */
        updateCountDisplay: function($requirement, current, min, max) {
            var $countDisplay = $requirement.find('.ppch-block-count');
            
            if (!$countDisplay.length) {
                // Create count display if it doesn't exist
                $countDisplay = $('<span class="ppch-block-count"></span>');
                $requirement.find('.status-label').append(' ').append($countDisplay);
            }
            
            var countText = this.formatCountText(current, min, max);
            $countDisplay.text(countText);
        },

        /**
         * Format count text for display
         */
        formatCountText: function(current, min, max) {
            if (min === max && min > 0) {
                return '(' + current + '/' + min + ')';
            } else if (min > 0 && max > 0 && max > min) {
                return '(' + current + ', need ' + min + '-' + max + ')';
            } else if (min > 0) {
                return '(' + current + ', need ' + min + '+)';
            } else if (max > 0) {
                return '(' + current + ', max ' + max + ')';
            }
            
            return '(' + current + ')';
        },

        /**
         * Get current block count for a specific block type
         */
        getBlockCount: function(blockName) {
            return this.blockCounts[blockName] || 0;
        },

        /**
         * Get all current block counts
         */
        getAllBlockCounts: function() {
            return Object.assign({}, this.blockCounts);
        },

        /**
         * Debug method to log current state
         */
        debug: function() {
            console.log('Block Checklists Debug Info:');
            console.log('Requirements:', this.requirements);
            console.log('Current Counts:', this.blockCounts);
            console.log('Editor Ready:', this.isEditorReady());
            
            if (this.isEditorReady()) {
                console.log('All Blocks:', this.getAllBlocks());
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        PPCHBlockChecklists.init();
    });

    // Expose to global scope for debugging
    window.PPCHBlockChecklists = PPCHBlockChecklists;

})(jQuery, window, document, window.PP_Checklists);
