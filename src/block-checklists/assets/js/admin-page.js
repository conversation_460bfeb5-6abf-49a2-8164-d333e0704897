/**
 * Admin page JavaScript for Block Checklists
 *
 * @package     PublishPress\ChecklistsPro\BlockChecklists
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2024 PublishPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0.0
 */

(function($, window, document) {
    'use strict';

    /**
     * Block Checklists Admin object
     */
    var PPCHBlockChecklists = {
        
        /**
         * Initialize the admin interface
         */
        init: function() {
            this.bindEvents();
            this.initializeFilters();
            this.setupFormValidation();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Rule selection changes
            $(document).on('change', '.ppch-rule-select', this.handleRuleChange);
            
            // Number input validation
            $(document).on('input', '.ppch-number-input', this.validateNumberInput);
            
            // Search functionality
            $('#ppch-search-blocks').on('input', this.debounce(this.filterBlocks, 300));
            
            // Filter changes
            $('#ppch-filter-category, #ppch-filter-namespace').on('change', this.filterBlocks);
            
            // Form submission
            $('form').on('submit', this.handleFormSubmit);
            
            // Bulk actions
            this.setupBulkActions();
        },

        /**
         * Handle rule selection changes
         */
        handleRuleChange: function() {
            var $select = $(this);
            var $countSettings = $select.closest('.ppch-post-type-settings').find('.ppch-count-settings');
            
            if ($select.val() === 'off') {
                $countSettings.slideUp(200);
                // Clear the min/max values when disabled
                $countSettings.find('.ppch-number-input').val(0);
            } else {
                $countSettings.slideDown(200);
            }
            
            // Update visual state
            PPCHBlockChecklists.updateBlockItemState($select.closest('.ppch-block-item'));
        },

        /**
         * Validate number inputs
         */
        validateNumberInput: function() {
            var $input = $(this);
            var value = parseInt($input.val());
            
            // Ensure non-negative values
            if (isNaN(value) || value < 0) {
                $input.val(0);
            }
            
            // Validate min/max relationship
            PPCHBlockChecklists.validateMinMaxRelationship($input);
            
            // Update visual state
            PPCHBlockChecklists.updateBlockItemState($input.closest('.ppch-block-item'));
        },

        /**
         * Validate min/max relationship
         */
        validateMinMaxRelationship: function($input) {
            var $container = $input.closest('.ppch-count-settings');
            var $minInput = $container.find('input[name*="[min]"]');
            var $maxInput = $container.find('input[name*="[max]"]');
            
            var minVal = parseInt($minInput.val()) || 0;
            var maxVal = parseInt($maxInput.val()) || 0;
            
            // Remove previous error states
            $minInput.removeClass('error');
            $maxInput.removeClass('error');
            
            // Validate: if both have values, max should be >= min
            if (minVal > 0 && maxVal > 0 && maxVal < minVal) {
                $maxInput.addClass('error');
                $maxInput.attr('title', 'Maximum value should be greater than or equal to minimum value');
            } else {
                $maxInput.removeAttr('title');
            }
        },

        /**
         * Update visual state of block item
         */
        updateBlockItemState: function($blockItem) {
            var hasActiveRules = false;
            
            $blockItem.find('.ppch-rule-select').each(function() {
                if ($(this).val() !== 'off') {
                    hasActiveRules = true;
                    return false; // break
                }
            });
            
            $blockItem.toggleClass('has-active-rules', hasActiveRules);
        },

        /**
         * Initialize filter functionality
         */
        initializeFilters: function() {
            // Set up search placeholder behavior
            var $searchInput = $('#ppch-search-blocks');
            $searchInput.attr('autocomplete', 'off');
            
            // Initialize filter state
            this.filterBlocks();
        },

        /**
         * Filter blocks based on search and filter criteria
         */
        filterBlocks: function() {
            var searchTerm = $('#ppch-search-blocks').val().toLowerCase().trim();
            var selectedCategory = $('#ppch-filter-category').val();
            var selectedNamespace = $('#ppch-filter-namespace').val();
            
            var visibleBlocks = 0;
            var visibleCategories = {};

            $('.ppch-block-item').each(function() {
                var $block = $(this);
                var blockTitle = $block.data('title') || '';
                var blockName = ($block.data('block-name') || '').toLowerCase();
                var blockKeywords = $block.data('keywords') || '';
                var blockCategory = $block.data('category') || '';
                var blockNamespace = $block.data('namespace') || '';
                
                var matchesSearch = !searchTerm || 
                                   blockTitle.indexOf(searchTerm) !== -1 || 
                                   blockName.indexOf(searchTerm) !== -1 || 
                                   blockKeywords.indexOf(searchTerm) !== -1;
                
                var matchesCategory = !selectedCategory || blockCategory === selectedCategory;
                
                var matchesNamespace = !selectedNamespace || 
                                      (selectedNamespace === 'core' && blockNamespace === 'core') ||
                                      (selectedNamespace === 'third-party' && blockNamespace !== 'core');
                
                if (matchesSearch && matchesCategory && matchesNamespace) {
                    $block.show();
                    visibleBlocks++;
                    visibleCategories[blockCategory] = true;
                } else {
                    $block.hide();
                }
            });

            // Show/hide category sections
            $('.ppch-category-section').each(function() {
                var $section = $(this);
                var category = $section.data('category');
                
                if (visibleCategories[category]) {
                    $section.show();
                } else {
                    $section.hide();
                }
            });

            // Show/hide no results message
            $('.ppch-no-results').toggle(visibleBlocks === 0);
            
            // Update results count if element exists
            PPCHBlockChecklists.updateResultsCount(visibleBlocks);
        },

        /**
         * Update results count display
         */
        updateResultsCount: function(count) {
            var $counter = $('.ppch-results-count');
            if ($counter.length) {
                $counter.text(count + ' blocks found');
            }
        },

        /**
         * Setup bulk actions
         */
        setupBulkActions: function() {
            // Add bulk action controls if they don't exist
            if (!$('.ppch-bulk-actions').length) {
                var bulkActionsHtml = '<div class="ppch-bulk-actions" style="margin-bottom: 20px;">' +
                    '<select id="ppch-bulk-rule">' +
                    '<option value="">Bulk set rule...</option>' +
                    '<option value="off">Disable All</option>' +
                    '<option value="only_display">Display Only</option>' +
                    '<option value="warning">Warning</option>' +
                    '<option value="block">Block Publishing</option>' +
                    '</select> ' +
                    '<button type="button" class="button" id="ppch-apply-bulk">Apply to Visible Blocks</button>' +
                    '</div>';
                
                $('.ppch-block-filters').after(bulkActionsHtml);
            }
            
            // Bind bulk action events
            $(document).on('click', '#ppch-apply-bulk', this.applyBulkAction);
        },

        /**
         * Apply bulk action to visible blocks
         */
        applyBulkAction: function() {
            var selectedRule = $('#ppch-bulk-rule').val();
            if (!selectedRule) {
                return;
            }
            
            var $visibleBlocks = $('.ppch-block-item:visible');
            var count = 0;
            
            $visibleBlocks.each(function() {
                var $block = $(this);
                $block.find('.ppch-rule-select').each(function() {
                    $(this).val(selectedRule).trigger('change');
                    count++;
                });
            });
            
            // Show feedback
            PPCHBlockChecklists.showNotice('Applied rule to ' + count + ' block configurations.', 'success');
            
            // Reset bulk selector
            $('#ppch-bulk-rule').val('');
        },

        /**
         * Setup form validation
         */
        setupFormValidation: function() {
            // Add CSS for error states
            if (!$('#ppch-validation-styles').length) {
                $('<style id="ppch-validation-styles">' +
                  '.ppch-number-input.error { border-color: #dc3232; background-color: #fef7f1; }' +
                  '.ppch-block-item.has-active-rules { border-left: 4px solid #0073aa; }' +
                  '</style>').appendTo('head');
            }
        },

        /**
         * Handle form submission
         */
        handleFormSubmit: function(e) {
            var hasErrors = false;
            
            // Validate all number inputs
            $('.ppch-number-input').each(function() {
                PPCHBlockChecklists.validateNumberInput.call(this);
                if ($(this).hasClass('error')) {
                    hasErrors = true;
                }
            });
            
            if (hasErrors) {
                e.preventDefault();
                PPCHBlockChecklists.showNotice('Please fix the validation errors before saving.', 'error');
                
                // Scroll to first error
                var $firstError = $('.ppch-number-input.error').first();
                if ($firstError.length) {
                    $('html, body').animate({
                        scrollTop: $firstError.offset().top - 100
                    }, 500);
                }
                
                return false;
            }
            
            // Show loading state
            PPCHBlockChecklists.showLoadingState();
        },

        /**
         * Show loading state during form submission
         */
        showLoadingState: function() {
            var $submitButton = $('input[type="submit"]');
            $submitButton.prop('disabled', true).val('Saving...');
            
            $('.ppch-block-item').addClass('loading');
        },

        /**
         * Show admin notice
         */
        showNotice: function(message, type) {
            type = type || 'info';
            
            var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            $('.ppch-block-checklists h1').after($notice);
            
            // Auto-dismiss after 5 seconds
            setTimeout(function() {
                $notice.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        },

        /**
         * Debounce function for search input
         */
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        PPCHBlockChecklists.init();
    });

    // Expose to global scope for debugging
    window.PPCHBlockChecklists = PPCHBlockChecklists;

})(jQuery, window, document);
